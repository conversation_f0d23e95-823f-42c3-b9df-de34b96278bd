{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

<div class="ph-panel" id="massupdateproducts-price-change">
    <div class="ph-panel-head">
        <div class="ph-panel-head-main">
            {l s='Masowa zmiana promocji produktów' mod='massupdateproducts'}
        </div>
    </div>
    <div class="ph-panel-content">
        <div class="price-change-form">
            <div class="form-group">
                <label>{l s='Metoda zmiany promocji' mod='massupdateproducts'}</label>
                <div class="radio-group">
                    <label class="radio-inline">
                        <input type="radio" name="method" value="0" checked="checked" />
                        {l s='Procent' mod='massupdateproducts'}
                    </label>
                    <label class="radio-inline">
                        <input type="radio" name="method" value="1" />
                        {l s='K<PERSON><PERSON>' mod='massupdateproducts'}
                    </label>
                </div>
            </div>

            <div class="form-group">
                <label>{l s='Typ zmiany' mod='massupdateproducts'}</label>
                <div class="radio-group">
                    <label class="radio-inline">
                        <input type="radio" name="type_change" value="0" checked="checked" />
                        {l s='Obniżka' mod='massupdateproducts'}
                    </label>
                    <label class="radio-inline">
                        <input type="radio" name="type_change" value="1" />
                        {l s='Wzrost' mod='massupdateproducts'}
                    </label>
                </div>
            </div>

            <div class="form-group">
                <label for="value_to_change">{l s='Wartość promocji' mod='massupdateproducts'}</label>
                <input type="text" id="value_to_change" name="value_to_change" class="form-control" placeholder="0.00" />
                <small class="help-block">
                    {l s='Wprowadź wartość promocji (procent lub kwotę w zależności od wybranej metody)' mod='massupdateproducts'}
                </small>
            </div>

            <div class="form-group">
                <label for="date_from">{l s='Data rozpoczęcia promocji' mod='massupdateproducts'}</label>
                <input type="text" id="date_from" name="date_from" class="form-control datepicker" placeholder="YYYY-MM-DD HH:MM:SS" />
                <small class="help-block">
                    {l s='Pozostaw puste dla promocji bez daty rozpoczęcia' mod='massupdateproducts'}
                </small>
            </div>

            <div class="form-group">
                <label for="date_to">{l s='Data zakończenia promocji' mod='massupdateproducts'}</label>
                <input type="text" id="date_to" name="date_to" class="form-control datepicker" placeholder="YYYY-MM-DD HH:MM:SS" />
                <small class="help-block">
                    {l s='Pozostaw puste dla promocji bez daty zakończenia' mod='massupdateproducts'}
                </small>
            </div>

            <div class="form-group">
                <button type="button" id="mass-update-specific-price" class="btn btn-primary">
                    <i class="fa fa-save" style="margin-right: 8px;"></i>
                    {l s='Zastosuj promocję do produktów z filtrów' mod='massupdateproducts'}
                </button>
            </div>
        </div>

        <div class="alert alert-info">
            <h4>{l s='Informacje o promocjach:' mod='massupdateproducts'}</h4>
            <ul>
                <li>{l s='Promocja zostanie zastosowana do wszystkich produktów z aktywnych filtrów' mod='massupdateproducts'}</li>
                <li>{l s='Jeśli produkt ma już promocję, zostanie ona zastąpiona nową' mod='massupdateproducts'}</li>
                <li>{l s='Procent: wartość od 0 do 100 (np. 20 = 20% obniżki)' mod='massupdateproducts'}</li>
                <li>{l s='Kwota: wartość w walucie sklepu (np. 10.50)' mod='massupdateproducts'}</li>
                <li>{l s='Daty w formacie: YYYY-MM-DD HH:MM:SS (np. 2024-01-01 00:00:00)' mod='massupdateproducts'}</li>
            </ul>
        </div>
    </div>
</div>
