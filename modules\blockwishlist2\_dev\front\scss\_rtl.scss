/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License version 3.0
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License version 3.0
 */
.lang-rtl {

  .products {

    article {

      .wishlist {

        &-button-add {
          right: inherit;
          left: 0.635rem;
        }
      }
    }
  }

  .wishlist {

    &-button {

      &-product {
        margin-left: 0;
        margin-right: 1.25rem;
      }
    }

    &-list {

      &-item {

        .dropdown-menu {
          right: inherit;
          left: 1.25rem;
        }

        &-right {

          .dropdown-menu {

            > button {
              text-align: right;
            }
          }
        }
      }
    }
  }
}
.favorite-container {
  display: flex;
  padding: .75rem;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #333;
}

.favorite-icon-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.favorite-icon {
  font-size: 32px;
  color: #666;
}

.favorite-count {
  position: absolute;
  top: -12px;
  right: -12px;
  background-color: #2196F3;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  border-radius: 50%;
  padding: 2px 6px;
}
