<?php

/**
 * 2010-2014 prestahelp.com
 *
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */
class MassUpdateSpecificPriceMassModel extends MassUpdateProductsAbstract
{

    public function __construct(&$module, &$object, &$context)
    {
        $this->settings_name = Tools::strtoupper('MassUpdateSpecificPriceMassModel');
        parent::__construct($module, $object, $context);
    }

    public function save(array $data)
    {
        $result = array();
        $end = false;
        if ($data)
            foreach ($data as $id_product) {
                $result[$id_product] = array(
                    'error' => true,
                    'message' => ''
                );
                $product = new Product($id_product);
                $product->rate = isset($this->object->tax_rates[$product->id_tax_rules_group]) ? $this->object->tax_rates[$product->id_tax_rules_group] : 0;

                if (!Validate::isLoadedObject($product)) {
                    $result[$id_product]['message'] = $this->module->l('Nie znaleziono produktu', $this->languages_name);
                    continue;
                }

                $type_change = (int)Tools::getValue('type_change', 0); // 0 = obniżka, 1 = wzrost
                $value_to_change = (float)Tools::getValue('value_to_change', 0);
                $method = (int)Tools::getValue('method', 0); // 0 = procent, 1 = kwota
                $date_from = Tools::getValue('date_from', '');
                $date_to = Tools::getValue('date_to', '');
                $overwrite_existing = (int)Tools::getValue('overwrite_existing', 0);

                if ($value_to_change <= 0) {
                    $result[$id_product]['message'] = $this->module->l('Wprowadź wartość promocji', $this->languages_name);
                    continue;
                }

                if ($overwrite_existing) {
                    // Sprawdź czy istnieje już promocja dla tego produktu (do nadpisania)
                    $existing_specific_price_id = Db::getInstance()->getValue('SELECT id_specific_price FROM `'._DB_PREFIX_.
                            'specific_price` WHERE id_product = '.
                            ((int)$product->id).' AND id_product_attribute = 0 AND id_shop IN (0, '.
                            ((int)$product->id_shop_default).') ORDER BY id_shop DESC');

                    $specific_price = new SpecificPrice($existing_specific_price_id);

                    if (!Validate::isLoadedObject($specific_price)) {
                        // Tworzymy nową promocję
                        $specific_price = new SpecificPrice();
                        $specific_price->id_product = $product->id;
                        $specific_price->id_product_attribute = 0;
                        $specific_price->id_currency = 0;
                        $specific_price->id_country = 0;
                        $specific_price->id_group = 0;
                        $specific_price->id_customer = 0;
                        $specific_price->id_shop = $product->id_shop_default;
                        $specific_price->price = -1;
                        $specific_price->from_quantity = 1;
                    }
                } else {
                    // Zawsze tworzymy nową promocję (nie nadpisujemy istniejących)
                    $specific_price = new SpecificPrice();
                    $specific_price->id_product = $product->id;
                    $specific_price->id_product_attribute = 0;
                    $specific_price->id_currency = 0;
                    $specific_price->id_country = 0;
                    $specific_price->id_group = 0;
                    $specific_price->id_customer = 0;
                    $specific_price->id_shop = $product->id_shop_default;
                    $specific_price->price = -1;
                    $specific_price->from_quantity = 1;
                }

                // Oblicz wartość promocji
                if ($method == 0) {
                    // Procent
                    $reduction_value = $value_to_change / 100;
                    $specific_price->reduction_type = 'percentage';
                } else {
                    // Kwota
                    $reduction_value = $value_to_change;
                    $specific_price->reduction_type = 'amount';
                }

                // Zastosuj typ zmiany (obniżka/wzrost)
                if ($type_change == 0) {
                    // Obniżka - normalna promocja
                    $specific_price->reduction = $reduction_value;
                } else {
                    // Wzrost - ujemna promocja (rzadko używane, ale możliwe)
                    $specific_price->reduction = -$reduction_value;
                }

                // Ustaw daty
                $specific_price->from = $date_from ? $date_from : '0000-00-00 00:00:00';
                $specific_price->to = $date_to ? $date_to : '0000-00-00 00:00:00';

                $errors = $specific_price->validateFields(false, true);
                if ($errors !== true) {
                    $result[$id_product]['message'] = is_bool($errors) ?
                            $this->module->l('Błąd walidacji', $this->languages_name) : (is_array($errors) ? implode(' | ', $errors) : $errors);
                    continue;
                }

                if ($specific_price->save()) {
                    $result[$id_product]['error'] = false;                  
					$result[$id_product]['message'] = $this->module->l('Promocja zapisana', $this->languages_name).': '.$product->name[$this->context->language->id];
                } else {
                    $result[$id_product]['message'] = $this->module->l('Problem z zapisem promocji', $this->languages_name);
                    continue;
                }
            } else
            $end = true;

        return array(
            'raport' => $result,
            'end' => $end
        );
    }

    public function display($result)
    {
        return $result;
    }

    public function extra()
    {
        $this->context->smarty->assign(array(
            'tax_rules_group' => $this->object->tax_rules_group
        ));
        return $this->object->createTemplate('specific_price_mass_change.tpl')->fetch();
    }

    public function hasCombination()
    {
        return false;
    }

}
