{"name": "blockwishlist", "version": "1.0.0", "description": "", "private": "true", "scripts": {"build": "webpack --progress --mode=production", "dev": "webpack --progress --debug --display-chunks --watch --mode=development", "watch": "npm run dev", "lint": "eslint -c .eslintrc.js --ext .js,.vue .", "lint-fix": "eslint -c .eslintrc.js --ext .js,.vue . --fix", "test": "mochapack --webpack-config .webpack/common.js --require tests/js/setup.js tests/js/**/*.spec.js"}, "author": "PrestaShop", "license": "AFL-3.0", "dependencies": {"apollo-cache-inmemory": "^1.6.5", "apollo-client": "^2.6.8", "apollo-link": "^1.2.13", "apollo-link-http": "^1.5.16", "apollo-link-schema": "^1.2.4", "graphql": "^14.0.2", "graphql-tag": "^2.10.3", "graphql-tools": "^4.0.7", "graphql-type-json": "^0.3.1", "sass": "^1.79.4", "v-click-outside": "^3.0.1", "vue": "^2.6.11", "vue-apollo": "^3.0.3", "vue-content-loader": "^0.2.3", "vue-i18n": "^8.15.1", "vue-resource": "^1.5.1", "vue-router": "^2.8.1", "vue-template-compiler": "^2.6.11", "vuex": "^2.5.0"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/eslint-parser": "^7.15.8", "@babel/plugin-transform-runtime": "^7.14.5", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.20.2", "@vue/test-utils": "^1.0.3", "babel-loader": "^8.1.0", "babel-polyfill": "^6.26.0", "babel-register": "^6.26.0", "chai": "^4.2.0", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^6.4.1", "css-loader": "^3.4", "eslint": "^7.5.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prestashop": "0.2.1", "eslint-import-resolver-webpack": "^0.12.1", "eslint-plugin-html": "^6.0.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-vue": "^7.0.0", "expect": "^26.1.0", "exports-loader": "^1.0.0", "expose-loader": "^1.0.0", "file-loader": "^5.0", "imports-loader": "^0.8.0", "jsdom": "^16.2.2", "jsdom-global": "^3.0.2", "loader-utils": "^1.4.2", "mini-css-extract-plugin": "^1.0.0", "mocha": "^7.1.2", "mochapack": "^2.1.4", "path": "^0.12.7", "postcss-loader": "^3.0.0", "postcss-preset-env": "^6.7.0", "sass-loader": "^10.4.1", "style-loader": "^1.1.3", "terser-webpack-plugin": "^3.0.6", "ts-loader": "^8.0.16", "ts-node": "^10.0.0", "typescript": "^4.1.4", "vue-loader": "^15", "vue-style-loader": "^4.1.2", "webpack": "^4.47.0", "webpack-cli": "^4.10.0", "webpack-fix-style-only-entries": "^0.6.1", "webpack-merge": "^5.7.3"}, "overrides": {"@jridgewell/gen-mapping": "0.3.5"}, "babel": {"presets": ["@babel/preset-env"]}}