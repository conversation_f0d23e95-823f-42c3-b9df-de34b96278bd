/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License version 3.0
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License version 3.0
 */
import initApp from '@components/init';
import ProductsListContainer from './ProductsListContainer.vue';

const props = [
  {
    name: 'url',
    type: String,
  },
  {
    name: 'title',
    type: String,
  },
  {
    name: 'noProductsMessage',
    type: String,
  },
  {
    name: 'addToCart',
    type: String,
  },
  {
    name: 'customizeText',
    type: String,
  },
  {
    name: 'wishlistProducts',
    type: String,
  },
  {
    name: 'wishlist',
    type: String,
  },
  {
    name: 'share',
    type: Boolean,
  },
  {
    name: 'quantityText',
    type: String,
  },
  {
    name: 'filter',
    type: String,
  },
  {
    name: 'listId',
    type: Number,
  },
];

initApp(ProductsListContainer, '.wishlist-products-container', props);
