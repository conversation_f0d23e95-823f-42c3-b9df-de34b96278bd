<?php
if (!defined('_PS_VERSION_')) {
    exit;
}

class BlockWishlistActionModuleFrontControllerCore extends BlockWishlistActionModuleFrontController
{
	public function init()
    {
        parent::init();
    }

    public function postProcess()
    {
        parent::postProcess();

        $action = Tools::getValue('action');
        $idProduct = (int)Tools::getValue('id_product');
        $qty = (int)Tools::getValue('quantity', 1);

        switch ($action) {
            case 'add':
                WishListCore::addProductCustom($idProduct, $qty);
                die(json_encode([
                    'success' => true,
                    'message' => $this->trans('Product added to wishlist!', [], 'Modules.Blockwishlistoverride.Shop')
                ]));

            case 'remove':
                WishListCore::removeProductCustom($idProduct);
                die(json_encode([
                    'success' => true,
                    'message' => $this->trans('Product removed from wishlist!', [], 'Modules.Blockwishlistoverride.Shop')
                ]));

            default:
			
                $params = Tools::getValue('params');
				if (method_exists($this, $action . 'Action')) {
					call_user_func([$this, $action . 'Action'], $params);
					exit;
				}
				
                break;
        }
    }
	/*
    public function postProcess()
    {
        $action = Tools::getValue('action');
        $idProduct = (int)Tools::getValue('id_product');
        $qty = (int)Tools::getValue('quantity', 1);

        switch ($action) {
            case 'add':
                WishListCore::addProductCustom($idProduct, $qty);
                die(json_encode(['success' => true, 'message' => 'Product added to wishlist!']));
            case 'remove':
                WishListCore::removeProductCustom($idProduct);
                die(json_encode(['success' => true, 'message' => 'Product removed from wishlist!']));
        }
    }*/
}