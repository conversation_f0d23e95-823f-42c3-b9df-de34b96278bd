# BlockWishList

## About

Adds a block containing the customer's and guest wishlists.
Task cron clear old wishlist: /modules/blockwishlist/cron/cleanWishlist.php

## Compatibility

PrestaShop: `8.0.0` or later

## Multistore compatibility

This module is compatible with the multistore :heavy_check_mark: <br/>
It can be configured differently from one store to another.<br/>
It can be configured quickly in the same way on all stores thanks to the all shops context or the group of shops.<br/>
It can be activated on one store and deactivated on another

## How to test

Link to specs : https://docs.prestashop-project.org/functional-documentation/functional-documentation/ux-ui/back-office/improve/modules/wishlist-module

- Configuration Tab
Edit wording and check in FO that the wording is updated
- Statistics Tab
Add multiple products to your wishlist, update the page, see that the Statistics are well updated

## Reporting issues

You can report issues with this module in the main PrestaShop repository. [Click here to report an issue][report-issue]. 

## Contributing

PrestaShop modules are open source extensions to the [PrestaShop e-commerce platform][prestashop]. Everyone is welcome and even encouraged to contribute with their own improvements!

Just make sure to follow our [contribution guidelines][contribution-guidelines].

## License

This module is released under the [Academic Free License 3.0][AFL-3.0] 

[report-issue]: https://github.com/PrestaShop/PrestaShop/issues/new/choose
[prestashop]: https://www.prestashop.com/
[contribution-guidelines]: https://devdocs.prestashop.com/1.7/contribute/contribution-guidelines/project-modules/
[AFL-3.0]: https://opensource.org/licenses/AFL-3.0
