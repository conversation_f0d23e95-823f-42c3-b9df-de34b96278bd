help:
	@egrep "^#" Makefile

# target: docker-build|db               - Setup/Build PHP & (node)JS dependencies
db: docker-build
docker-build: build-back build-front

build-back:
	docker compose run --rm php sh -c "composer install"

build-back-prod:
	docker compose run --rm php sh -c "composer install --no-dev -o"

build-front:
	docker compose run --rm node sh -c "npm install"
	docker compose run --rm node sh -c "npm run build"
