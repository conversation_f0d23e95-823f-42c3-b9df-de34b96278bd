<?php
/**
 *  Moduł masowej aktualizacji produktów
 *
 *  2010-2020 prestahelp.com
 *
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2020 prestahelp.com
 *  @license   Sharewares
 *  @extension <PERSON><PERSON><PERSON>rz<PERSON> <<EMAIL>>
 */

class MassUpdateProducts extends Module
{
    public $current_lang;
    public $languages;
    public $db;
    public $path_full;
    protected $default_options = array(
        1,
        4,
        6,
        8,
        13,
        14
    );
    public $options = array();

    public function __construct()
    {
        $this->name = 'massupdateproducts';
        $this->tab = 'administration';
        $this->version = '5.0.0';
        $this->author = 'prestahelp.com';
        $this->module_key = '30a21b66feb496c60d1999e7e8d93e4e';
        $this->need_instance = 1;
        $this->path_full = _PS_MODULE_DIR_.$this->name.'/';
        $this->bootstrap = true;
        parent::__construct();

        $this->displayName = $this->l('Mass Update Products');
        $this->description = $this->l('Perform mass updates on all your products (price, weight, and features)');

        $this->current_lang = $this->context->cookie->id_lang;
        $this->languages = Language::getLanguages(true);
        $this->sub_tabs = array(
            array(
                'name' => 'Mass Update Products',
                'class_name' => 'AdminMassUpdateProducts',
                'pl' => 'Masowa aktualizacja produktów',
                'parent' => 'AdminCatalog'
            )
        );
        if (Configuration::get(Tools::strtoupper($this->name).'_OPTIONS')) {
            $this->options = unserialize(Configuration::get(Tools::strtoupper($this->name).'_OPTIONS'));
        }
    }

    public function install()
    {
        Configuration::updateValue(Tools::strtoupper($this->name).'_LIMIT', 20);
        Configuration::updateValue(Tools::strtoupper($this->name).'_OPTIONS', serialize($this->default_options));
        return parent::install() && $this->installSubTabs() && $this->installSql();
    }

    public function uninstall()
    {
        Configuration::deleteByName(Tools::strtoupper($this->name).'_LIMIT');
        Configuration::deleteByName(Tools::strtoupper($this->name).'_OPTIONS');
        return $this->uninstallSubTabs() && parent::uninstall();
    }

    private function installSubTabs()
    {
        foreach ($this->sub_tabs as $sub_tab) {
            $tab = new Tab();
            $tab->active = true;
            $tab->class_name = $sub_tab['class_name'];
            foreach ($this->languages as $lang) {
                if ($lang['iso_code'] == 'pl') {
                    $tab->name[$lang['id_lang']] = $sub_tab['pl'];
                } else {
                    $tab->name[$lang['id_lang']] = $sub_tab['name'];
                }
            }
            $tab->module = $this->name;
            $tab->id_parent = (int)Tab::getIdFromClassName($sub_tab['parent']);
            if (!$tab->add()) {
                return false;
            }
        }
        return true;
    }

    private function uninstallSubTabs()
    {
        foreach ($this->sub_tabs as $sub_tab) {
            $id_tab = Tab::getIdFromClassName($sub_tab['class_name']);
            if (!$id_tab) {
                return false;
            }
            $tab = new Tab($id_tab);
            if (!$tab->delete()) {
                return false;
            }
        }
        return true;
    }

    public function getPath()
    {
        return $this->_path;
    }

    public function getLocalPath()
    {
        return $this->local_path;
    }

    public function getContent()
    {
        $output = '';

        if (Tools::getIsset('submitSetUpdate')) {
            $update410_sql = Db::getInstance()->getRow('SELECT COUNT(*) as count FROM information_schema.tables WHERE `table_name` = "'._DB_PREFIX_.'massupdateproduct_backup"');
            if ((int)$update410_sql['count'] == 0) {
                $this->installSql();
            }
            Tools::redirect($_SERVER['HTTP_REFERER']);
        }

        $update = false;
        $update410_sql = Db::getInstance()->getRow('SELECT COUNT(*) as count FROM information_schema.tables WHERE `table_name` = "'._DB_PREFIX_.'massupdateproduct_backup"');
        if ((int)$update410_sql['count'] == 0) {
            $update = true;
        }

        $backupDir = $this->context->link->getAdminLink('AdminMassUpdateProductsBackUp');
        $this->context->smarty->assign(array(
            'backupDir' => $backupDir,
            'update' => $update,

        ));
        $output .= $this->context->smarty->fetch($this->local_path . 'views/templates/admin/module.tpl');
        return $output;
    }

    private function installSql()
    {
        if (Db::getInstance()->execute('CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'massupdateproduct_backup` (
				`id_backup` int(11) NOT NULL AUTO_INCREMENT,
                `info` TEXT,
                `file` varchar(500),
                `dir` varchar(500),
                `date_restore` DATETIME NULL,
            	`date_add` datetime,
            	`date_upd` datetime,
                PRIMARY KEY  (`id_backup`)
			) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=UTF8;')) {
            return true;
        }
        return false;
    }

}
