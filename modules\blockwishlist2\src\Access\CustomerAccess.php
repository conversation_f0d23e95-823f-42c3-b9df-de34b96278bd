<?php
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License version 3.0
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License version 3.0
 */

namespace PrestaShop\Module\BlockWishList\Access;

use Customer;
use Tools;
use Validate;
use WishList;
use Guest;

class CustomerAccess
{
    /**
     * @var Customer
     */
    private $customer;
    private $id_guest;

    public function __construct(Customer $customer, $id_guest)
    {
        $this->customer = $customer;
        $this->id_guest = $id_guest;
    }

    /**
     * @return bool
     */
    public function hasReadAccessToWishlist(WishList $wishlist)
    {
        // Wishlist is shared
        if (!empty($wishlist->token) && Tools::getIsset('token')) {
            return true;
        }

        return $this->hasWriteAccessToWishlist($wishlist);
    }

    /**
     * @return bool
     */
    public function hasWriteAccessToWishlist(WishList $wishlist)
    {
		$guest = new Guest($this->id_guest);
		//echo $this->id_guest;
		//var_dump(false === Validate::isLoadedObject($this->customer));
		//var_dump(false === Validate::isLoadedObject($guest));
        if (false === Validate::isLoadedObject($this->customer) && false === Validate::isLoadedObject($guest)) {			
            return false;
        }		

        return ((int) $wishlist->id_customer) === (int)$this->customer->id || ((int) $wishlist->id_guest) === (int)$this->id_guest;
    }
}
