<?php
if (!defined('_PS_VERSION_')) {
    exit;
}

class WishListCore extends WishList
{
	
    /**
     * Przykładowa metoda: dodanie produktu do wishlisty
     * - Zależnie od tego, czy mamy id_customer czy id_guest
     * - <PERSON><PERSON><PERSON> brak id_guest – zapis do $_SESSION
     */
    public static function addProductCustom($idProduct, $quantity = 1)
    {
        $context = Context::getContext();
        $idCustomer = (int)$context->customer->id;
        $idGuest    = (int)$context->cookie->id_guest;

        if ($idCustomer > 0) {
            // Zaladowany => zapis w standardowej tabeli ps_wishlist (w oryginalnym module)
            // lub ps_wishlist_product...
            return self::addProductForCustomer($idCustomer, $idProduct, $quantity);
        } elseif ($idGuest > 0) {
            // gość zarejestrowany w ps_guest => zapis do np. ps_wishlist_guest
            return self::addProductForGuest($idGuest, $idProduct, $quantity);
        } else {
            // Brak id_guest => zapis w sesji
            if (!isset($_SESSION['wishlist_products'])) {
                $_SESSION['wishlist_products'] = [];
            }

            if (!isset($_SESSION['wishlist_products'][$idProduct])) {
                $_SESSION['wishlist_products'][$idProduct] = 0;
            }
            $_SESSION['wishlist_products'][$idProduct] += $quantity;

            return true;
        }
    }

    /**
     * Dodawanie produktu do wishlisty typowo przypisanej do ZALOGOWANEGO użytkownika
     */
    public static function addProductForCustomer($idCustomer, $idProduct, $quantity = 1)
    {
        if (!$idCustomer || !$idProduct) {
            return false;
        }
        // Przykładowy zapis - w oryginalnym blockwishlist mogłoby to wyglądać inaczej
        // i wymagać $idWishlist = ...
        // Tutaj tylko pokazujemy ideę: dopisz rekord do ps_wishlist_product
        $sql = 'INSERT INTO `'._DB_PREFIX_.'wishlist_product` (id_wishlist, id_product, quantity, date_add)
                SELECT w.id_wishlist, '.(int)$idProduct.', '.(int)$quantity.', NOW()
                FROM `'._DB_PREFIX_.'wishlist` w
                WHERE w.id_customer='.(int)$idCustomer.' 
                ORDER BY w.id_wishlist ASC
                LIMIT 1';

        return Db::getInstance()->execute($sql);
    }

    /**
     * Dodawanie produktu do wishlisty gościa
     */
    public static function addProductForGuest($idGuest, $idProduct, $quantity = 1)
    {
        if (!$idGuest || !$idProduct) {
            return false;
        }

        $sql = 'INSERT INTO `'._DB_PREFIX_.'wishlist_guest`
                (id_guest, id_product, quantity, date_add, date_upd)
                VALUES (
                    '.(int)$idGuest.',
                    '.(int)$idProduct.',
                    '.(int)$quantity.',
                    NOW(),
                    NOW()
                )';

        return Db::getInstance()->execute($sql);
    }

    /**
     * Usuwanie produktu z wishlisty (zależnie od usera/gościa)
     */
    public static function removeProductCustom($idProduct)
    {
        $context = Context::getContext();
        $idCustomer = (int)$context->customer->id;
        $idGuest    = (int)$context->cookie->id_guest;

        if ($idCustomer > 0) {
            // Usunięcie z ps_wishlist_product (przypisanego do customer)
            $sql = 'DELETE wp
                    FROM `'._DB_PREFIX_.'wishlist_product` wp
                    JOIN `'._DB_PREFIX_.'wishlist` w ON (wp.id_wishlist = w.id_wishlist)
                    WHERE w.id_customer='.(int)$idCustomer.'
                      AND wp.id_product='.(int)$idProduct;
            return Db::getInstance()->execute($sql);
        } elseif ($idGuest > 0) {
            // Usunięcie z ps_wishlist_guest
            $sql = 'DELETE FROM `'._DB_PREFIX_.'wishlist_guest`
                    WHERE id_guest='.(int)$idGuest.'
                      AND id_product='.(int)$idProduct;
            return Db::getInstance()->execute($sql);
        } else {
            // Usunięcie z sesji
            if (isset($_SESSION['wishlist_products'][$idProduct])) {
                unset($_SESSION['wishlist_products'][$idProduct]);
            }
            return true;
        }
    }
	/*
    public static function addProductCustom($idProduct, $quantity = 1)
    {
        $context = Context::getContext();
        $idCustomer = (int) $context->customer->id;
        $idGuest    = (int) $context->cookie->id_guest;

        if ($idCustomer > 0) {
            return self::addProductForCustomer($idCustomer, $idProduct, $quantity);
        } elseif ($idGuest > 0) {
            return self::addProductForGuest($idGuest, $idProduct, $quantity);
        } else {
            if (!isset($_SESSION['wishlist_products'])) {
                $_SESSION['wishlist_products'] = [];
            }
            $_SESSION['wishlist_products'][$idProduct] = ($_SESSION['wishlist_products'][$idProduct] ?? 0) + $quantity;
            return true;
        }
    }

    public static function removeProductCustom($idProduct)
    {
        $context = Context::getContext();
        $idCustomer = (int)$context->customer->id;
        $idGuest    = (int)$context->cookie->id_guest;

        if ($idCustomer > 0) {
            $sql = 'DELETE FROM '._DB_PREFIX_.'wishlist_product WHERE id_product='.(int)$idProduct;
            return Db::getInstance()->execute($sql);
        } elseif ($idGuest > 0) {
            $sql = 'DELETE FROM '._DB_PREFIX_.'wishlist_guest WHERE id_product='.(int)$idProduct;
            return Db::getInstance()->execute($sql);
        } else {
            unset($_SESSION['wishlist_products'][$idProduct]);
            return true;
        }
    }*/
}