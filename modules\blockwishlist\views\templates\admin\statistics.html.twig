{#**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License version 3.0
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License version 3.0
 *#}
{% extends '@PrestaShop/Admin/layout.html.twig' %}

{% block content %}
  {% if shopId is same as(null) %}
    <div class="alert medium-alert alert-warning" role="alert">
      <p class="alert-text">
        {{ 'You need to choose a specific shop to display the top 10 most added products'|trans({}, 'Modules.Blockwishlist.Admin') }}
      </p>
    </div>
  {% endif %}

  <div class="row justify-content-center wishlist-stats">
    <div class="col-xl-10">
      <div class="card">
        <h3 class="card-header">
          <i class="material-icons">star</i> {{ 'Top 10 most added products'|trans({}, 'Modules.Blockwishlist.Admin') }}
        </h3>

        <div class="card-body">
          <div class="card-text">
            <div class="row wishlist-stats-topbar">
              <div class="btn-group">
                <button class="btn btn-default active" data-tab="1">Day</button>
                <button class="btn btn-default" data-tab="2">Month</button>
                <button class="btn btn-default" data-tab="3">Year</button>
                <button class="btn btn-default" data-tab="4">All time</button>
              </div>

              <button class="btn btn-default refresh js-refresh">
                <i class="material-icons">history</i> {{ 'Refresh'|trans({}, 'Modules.Blockwishlist.Admin') }}
              </button>
            </div>

            <div class="wishlist-tab active" data-tab="1">
              {% include '@PrestaShop/Admin/Common/Grid/grid.html.twig' with {'grid': allTimeStatisticsGrid} %}
            </div>

            <div class="wishlist-tab" data-tab="2">
              {% include '@PrestaShop/Admin/Common/Grid/grid.html.twig' with {'grid': currentYearStatisticsGrid} %}
            </div>

            <div class="wishlist-tab" data-tab="3">
              {% include '@PrestaShop/Admin/Common/Grid/grid.html.twig' with {'grid': currentMonthStatisticsGrid} %}
            </div>

            <div class="wishlist-tab" data-tab="4">
              {% include '@PrestaShop/Admin/Common/Grid/grid.html.twig' with {'grid': currentDayStatisticsGrid} %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block javascripts %}
  {{ parent() }}

  <script>
    var blockwishlistModule = {
      'resetCacheUrl': '{{ url('blockwishlist_statistics_reset')|e('js') }}',
    };
  </script>
  <script src="{{ asset('../modules/blockwishlist/public/backoffice.bundle.js') }}"></script>
{% endblock %}
