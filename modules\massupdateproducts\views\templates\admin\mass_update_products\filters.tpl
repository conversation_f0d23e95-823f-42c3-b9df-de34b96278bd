{*
* 2010-2014 prestahelp.com
* 
*  <AUTHOR>    <<EMAIL>>
*  @copyright 2010-2014 prestahelp.com
*  @license   Shareware
*}

<script type="text/javascript">
    var $price_from = {$price_from|intval};
    var $price_to = {$price_to|intval};
    var $quantity_from = {$quantity_from|intval};
    var $quantity_to = {$quantity_to|intval};
    var $weight_from = {$weight_from|intval};
    var $weight_to = {$weight_to|intval};
</script>
<div class="filter-mask"></div>
<div class="filter-loading">{l s='Loading' mod='massupdateproducts'}...</div>
<div class="ph-panel filters-panel" id="massupdateproducts-filters">
    <div class="ph-panel-head">
        <div class="ph-panel-head-main">
            <button class="massbtn show_checkbox">
                <i class="fa fa-plus-square"></i>&nbsp;{l s='Show' mod='massupdateproducts'}
            </button>
            <button class="massbtn hide_checkbox hidden">
                <i class="fa fa-minus-square"></i>&nbsp;{l s='Hide' mod='massupdateproducts'}
            </button>
            {l s='Filters' mod='massupdateproducts'}
        </div>
    </div>
    <div class="ph-panel-content" id="filters-panel">
        <div class="filters-panel-left">
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Categories' mod='massupdateproducts'}
                </div>
                <div class="filters-panel-element-content">
                    <div class="filter_category">
                        <input class="filter_category_all" type="checkbox" /> {l s='Check all' mod='massupdateproducts'}
                    </div>
                    <div class="filter-categories">
                        {if isset($categories) && $categories}
                            {foreach $categories as $category}
                                <div class="filter_category" style="padding-left: {(15*$category['level_depth'])|intval}px;">
                                    <input value="{$category['id_category']|intval}" type="checkbox" name="filter_category_element[]" class="filter_category_element" />
                                    {$category['name']|strval}
                                </div>
                            {/foreach}
                        {/if}
                    </div>
                </div>
            </div>
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Manufacturers' mod='massupdateproducts'}
                </div>
                <div class="filters-panel-element-content">
                    <div class="filter_manufacturer">
                        <input class="filter_manufacturer_all" type="checkbox" /> {l s='Check all' mod='massupdateproducts'}
                    </div>
                    <div class="filter-manufacturers">
                        {if isset($manufacturers) && $manufacturers}
                            {foreach $manufacturers as $manufacturer}
                                <div class="filter_manufacturer">
                                    <input value="{$manufacturer['id_manufacturer']|intval}" type="checkbox" name="filter_manufacturer_element[]" class="filter_manufacturer_element" />
                                    {$manufacturer['name']|strval}
                                </div>
                            {/foreach}
                        {/if}
                    </div>
                </div>
            </div>
            <div class="search">
                <button id="filter" class="massbtn">
                    <i class="fa fa-filter"></i>&nbsp;{l s='filter' mod='massupdateproducts'}
                </button>
            </div>
            <div class="reset">
                <button id="reset" class="massbtn">
                    <i class="fa fa-times"></i>&nbsp;{l s='reset' mod='massupdateproducts'}
                </button>
            </div>
        </div>
        <div class="filters-panel-middle">
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Product' mod='massupdateproducts'}
                </div>
                <div class="filter-panel-element-content">
                    <input type="text" style="width: 100%;" name="filter_product_name" class="filter_product_name" />
                </div>
            </div>
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Price' mod='massupdateproducts'}
                    <input type="text" name="filter_range_price" readonly class="input_range filter_range_price" />
                </div>
                <div class="filter-panel-element-content">
                    <div id="slider-range-price"></div>
                </div>
            </div>
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Quantity' mod='massupdateproducts'}
                    <input type="text" name="filter_range_quantity"  readonly class="input_range filter_range_quantity" />
                </div>
                <div class="filter-panel-element-content">
                    <div id="slider-range-quantity"></div>
                </div>
            </div>
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Weight' mod='massupdateproducts'}
                    <input type="text" name="filter_range_weight" readonly class="input_range filter_range_weight" />
                </div>
                <div id="slider-range-weight"></div>
            </div>
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Date add' mod='massupdateproducts'}
                </div>
                <div class="filter-panel-element-content">
                    <input type="text" name="filter_date_add_from" class="filter_date_add_from filter-date" /> -
                    <input type="text" name="filter_date_add_to" class="filter_date_add_to filter-date" />
                </div>
            </div>
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Date update' mod='massupdateproducts'}
                </div>
                <div class="filter-panel-element-content">
                    <input type="text" name="filter_date_update_from" class="filter_date_update_from filter-date" /> -
                    <input type="text" name="filter_date_update_to" class="filter_date_update_to filter-date" />
                </div>
            </div>
        </div>
        <div class="filters-panel-right">
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Active' mod='massupdateproducts'}
                </div>
                <select name="filter_active" class="filter_active">
                    <option value="0"></option>
                    <option value="1">{l s='Yes' mod='massupdateproducts'}</option>
                    <option value="2">{l s='No' mod='massupdateproducts'}</option>
                </select>
            </div>
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Category default' mod='massupdateproducts'}
                </div>
                <div class="filter-category-default">
                    <input class="filter_category_default_all" type="checkbox" /> {l s='Check all' mod='massupdateproducts'}
                </div>
                <div class="filter-categories-default">
                    {if isset($categories) && $categories}
                        {foreach $categories as $category}
                            <div class="filter-category-default" style="padding-left: {(15*$category['level_depth'])|intval}px;">
                                <input value="{$category['id_category']|intval}" type="checkbox" name="filter_category_default[]" class="filter-category-default-input" />
                                {$category['name']|strval}
                            </div>
                        {/foreach}
                    {/if}
                </div>
            </div>
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Promotion' mod='massupdateproducts'}
                </div>
                <select name="filter_promotion" class="filter_promotion">
                    <option value="0"></option>
                    <option value="1">{l s='Yes' mod='massupdateproducts'}</option>
                    <option value="2">{l s='No' mod='massupdateproducts'}</option>
                </select>
            </div>
            <div class="filters-panel-element">
                <div class="filters-panel-element-head">
                    {l s='Show products with empty stock' mod='massupdateproducts'}
                </div>
                <select name="filter_show_empty" class="filter_show_empty">
                    <option value="0"></option>
                    <option value="1">{l s='Yes' mod='massupdateproducts'}</option>
                    <option value="2">{l s='No' mod='massupdateproducts'}</option>
                </select>
            </div>
        </div>
    </div>
</div>