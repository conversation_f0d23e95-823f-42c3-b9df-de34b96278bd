/**
 * 2010-2014 prestahelp.com
 *
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */

function mass_save_all(params) {
    if (typeof params === 'undefined') {
        params = {};
    }

    // Pobierz produkty z filtrów (tak jak w save_price_change.js)
    var $products = $('#massupdateproducts-products');
    var $data = $products.data('products');

    if (!$data || Object.keys($data).length === 0) {
        $.notify('Brak produktów do aktualizacji. Użyj filtrów aby wybrać produkty.', 'error', {
            autoHideDelay: 3000
        });
        return;
    }

    var products = {};
    var i = 0;
    $.each($data, function(key, value) {
        products[i++] = value.id_product;
    });

    if (!params.value_to_change || parseFloat(params.value_to_change) <= 0) {
        $.notify('<PERSON><PERSON><PERSON><PERSON><PERSON> prawidłow<PERSON> wartość promocji', 'error', {
            autoHideDelay: 3000
        });
        return;
    }

    if (params.method == '0' && parseFloat(params.value_to_change) > 100) {
        $.notify('Wartość procentowa nie może być większa niż 100%', 'error', {
            autoHideDelay: 3000
        });
        return;
    }

    var data = {
        is_ajax: 1,
        save_mass: 1,
        dataSend: products,
        method: params.method || 0,
        type_change: params.type_change || 0,
        value_to_change: params.value_to_change || 0,
        date_from: params.date_from || '',
        date_to: params.date_to || '',
        overwrite_existing: params.overwrite_existing || 0
    };

    $.ajax({
        type: 'POST',
        data: data,
        dataType: 'json',
        beforeSend: function() {
            $massupdateproductsProcess = true;
            $('.saving-mask').show();
            $('.saving-loading').show();
            $('#mass-update-specific-price').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Zapisuje...');
        },
        success: function(response) {
            if (response && response.raport) {
                var success_count = 0;
                var error_count = 0;
                var messages = [];

                $.each(response.raport, function(product_id, result) {
                    $.notify(result.message, result.error ? 'error' : 'success', {
                        autoHideDelay: 2000
                    });
                });

                // Nie przeładowuj strony - użyj powiadomień jak w save_price_change.js
            } else {
                $.notify('Błąd podczas aktualizacji promocji', 'error', {
                    autoHideDelay: 3000
                });
            }
        },
        error: function() {
            $.notify('Błąd połączenia z serwerem', 'error', {
                autoHideDelay: 3000
            });
        },
        complete: function() {
            $massupdateproductsProcess = false;
            $('.saving-mask').hide();
            $('.saving-loading').hide();
            $('#mass-update-specific-price').prop('disabled', false).html('<i class="fa fa-save" style="margin-right: 8px;"></i>Zastosuj promocję do produktów z filtrów');
        }
    });
}

$(document).ready(function() {
    // Inicjalizacja datepicker dla dat promocji
    if ($.fn.datepicker) {
        $('.datepicker').datepicker({
            dateFormat: 'yy-mm-dd',
            changeMonth: true,
            changeYear: true
        });
    }

    // Walidacja formularza
    $('#value_to_change').on('input', function() {
        var value = parseFloat($(this).val());
        var method = $('input[name="method"]:checked').val();
        
        if (method == '0' && value > 100) {
            $(this).addClass('error');
            $(this).next('.help-block').text('Wartość procentowa nie może być większa niż 100%').addClass('text-danger');
        } else if (value <= 0) {
            $(this).addClass('error');
            $(this).next('.help-block').text('Wartość musi być większa od 0').addClass('text-danger');
        } else {
            $(this).removeClass('error');
            $(this).next('.help-block').removeClass('text-danger');
            if (method == '0') {
                $(this).next('.help-block').text('Wprowadź wartość promocji (procent lub kwotę w zależności od wybranej metody)');
            }
        }
    });

    // Zmiana metody (procent/kwota)
    $('input[name="method"]').change(function() {
        var method = $(this).val();
        var value_input = $('#value_to_change');

        if (method == '0') {
            value_input.attr('placeholder', '0-100');
            value_input.next('.help-block').text('Wprowadź wartość procentową (0-100)');
        } else {
            value_input.attr('placeholder', '0.00');
            value_input.next('.help-block').text('Wprowadź kwotę promocji w walucie sklepu');
        }

        // Ponowna walidacja
        value_input.trigger('input');
    });

    // Obsługa przycisku masowej aktualizacji
    $('#mass-update-specific-price').click(function() {
        var method = $('input[name="method"]:checked').val();
        var type_change = $('input[name="type_change"]:checked').val();
        var value_to_change = $('#value_to_change').val();
        var date_from = $('#date_from').val();
        var date_to = $('#date_to').val();
        var overwrite_existing = $('#overwrite_existing').is(':checked') ? 1 : 0;

        if (!value_to_change || parseFloat(value_to_change) <= 0) {
            $.notify('Wprowadź prawidłową wartość promocji', 'error', {
                autoHideDelay: 3000
            });
            return;
        }

        if (method == '0' && parseFloat(value_to_change) > 100) {
            $.notify('Wartość procentowa nie może być większa niż 100%', 'error', {
                autoHideDelay: 3000
            });
            return;
        }

        // Wywołanie funkcji masowej aktualizacji
        mass_save_all({
            method: method,
            type_change: type_change,
            value_to_change: value_to_change,
            date_from: date_from,
            date_to: date_to,
            overwrite_existing: overwrite_existing
        });
    });
});
