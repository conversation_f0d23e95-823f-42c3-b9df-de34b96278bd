<?php
require_once dirname(__FILE__, 4).'/config/config.inc.php';
require_once dirname(__FILE__, 4).'/init.php';

$sql = 'DELETE FROM '._DB_PREFIX_.'wishlist_product where id_wishlist in (select id_wishlist from '._DB_PREFIX_.'wishlist WHERE date_add < DATE_SUB(NOW(), INTERVAL 1 MONTH))';
Db::getInstance()->execute($sql);

$sql = 'DELETE FROM '._DB_PREFIX_.'wishlist WHERE date_add < DATE_SUB(NOW(), INTERVAL 1 MONTH)';
Db::getInstance()->execute($sql);

echo "Old guest wishlists removed.
";