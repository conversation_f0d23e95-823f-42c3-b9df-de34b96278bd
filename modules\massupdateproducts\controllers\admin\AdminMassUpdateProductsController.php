<?php
/**
 * 2010-2014 prestahelp.com
 *
 *  <AUTHOR>    <<EMAIL>>
 *  @copyright 2010-2014 prestahelp.com
 *  @license   Shareware
 */
if (!defined('_PS_VERSION_'))
    exit;

require_once _PS_MODULE_DIR_.'massupdateproducts/classes/MassUpdateProductsAbstract.php';
require_once _PS_MODULE_DIR_.'massupdateproducts/classes/MassUpdateProductsModel.php';
require_once __DIR__.'/../../classes/AuthDSMassUpdate.php';

class AdminMassUpdateProductsController extends ModuleAdminController
{

    protected $available_tabs = array();
    protected $default_tab = 'massupdateproducts';
    protected $available_tabs_lang = array();
    protected $initial_filters = array();
    protected $submitted_tabs;
    protected $tab_display;
    public $shop_group;
    public $tax_rules_group = array();
    public $tax_rates = array();
    protected $limit_conf;
    public $languages = array();
    protected $options = array();
    public $img_type;
    public $img_type_home;
    protected $category_default;
    protected $class_tabs;
    protected $template_tabs;
    protected $active_all = false;

    protected $elements = 20;

    protected $init_filters = true;

    public function __construct()
    {
        $this->bootstrap = true;
        $this->img_type = ImageType::getFormattedName('large');
        $this->img_type_home = ImageType::getFormattedName('large');
        $this->category_default = ImageType::getFormattedName('category');
        parent::__construct();

        if (Shop::isFeatureActive())
            $this->active_all = !$this->context->cookie->shopContext || preg_match('/g-/', $this->context->cookie->shopContext);

        $this->available_tabs_lang = array(
            'massupdateproducts' => $this->l('Mass update products'),
            'massupdatefeatureproducts' => $this->l('Mass update feature products'),
            'masschangedescriptionofproduct' => $this->l('Mass change description of product'),
            'masschangepriceofproduct' => $this->l('Mass change tax excl price of product'),
            'masschangespecificpriceofproduct' => $this->l('Mass change specific price of product'),
            //'masschangeshippingextracostofproduct' => $this->l('Mass change additional shipping costs of product'),
            'masschangecategoriesofproduct' => $this->l('Mass change categories of product'),
            'masschangecarrierofproduct' => $this->l('Mass change carrier of product'),
            'masschangecarrierbycategoryofproduct' => $this->l('Mass change carrier of product by filters'),
            'masschangemetatagsofproduct' => $this->l('Mass change data of product'),
            'masschangeimageofproduct' => $this->l('Mass change images product'),
            'masschangecombinationsofproduct' => $this->l('Mass change combinations of product'),
            'masschangeaccessoriesofproduct' => $this->l('Mass change accessories of product'),
            'massupdatepricebyid' => $this->l('Mass change products price by id product'),
            'masschangeproductavailable' => $this->l('Mass change product available'),
        );

        if ($this->context->shop->getContext() != Shop::CONTEXT_GROUP) {
            $this->available_tabs = array_merge($this->available_tabs, array(
                'massupdateproducts' => 0,
                'massupdatefeatureproducts' => 1,
                'masschangedescriptionofproduct' => 2,
                'masschangepriceofproduct' => 4,
                'masschangespecificpriceofproduct' => 5,
                //'masschangeshippingextracostofproduct' => 6,
                'masschangecategoriesofproduct' => 8,
                'masschangecarrierofproduct' => 9,
                'masschangecarrierbycategoryofproduct' => 10,
                'masschangemetatagsofproduct' => 11,
                'masschangeimageofproduct' => 12,
                'masschangecombinationsofproduct' => 13,
                'masschangeaccessoriesofproduct' => 14,
                'massupdatepricebyid' => 15,
                'masschangeproductavailable' => 16
            ));
        }

        $this->class_tabs = array(
            'massupdateproducts' => 'MassUpdateProductsModel',
            'masschangecategoriesofproduct' => 'MassUpdateCategoriesProductsModel',
            'masschangemetatagsofproduct' => 'MassUpdateMetaTagsProductsModel',
            'masschangecarrierofproduct' => 'MassUpdateCarrierProductsModel',
            'massupdatefeatureproducts' => 'MassUpdateFeatureProductsModel',
            'masschangedescriptionofproduct' => 'MassUpdateDescriptionProductsModel',
            'masschangespecificpriceofproduct' => 'MassUpdateSpecificProductsModel',
            'masschangepriceofproduct' => 'MassUpdatePriceProductsModel',
            'masschangecarrierbycategoryofproduct' => 'MassUpdateCarrierByFProductsModel',
            'masschangecombinationsofproduct' => 'MassUpdateCombinationProductsModel',
            'masschangeimageofproduct' => 'MassUpdateImageProductsModel',
            'masschangeaccessoriesofproduct' => 'MassUpdateAccessoriesProductModel',
            'massupdatepricebyid' => 'MassUpdatePriceById',
            'masschangeproductavailable' => 'MassUpdateProductsAvailableModel'
        );

        $this->template_tabs = array(
            'massupdateproducts' => 'table_schema',
            'masschangecategoriesofproduct' => 'table_schema',
            'masschangemetatagsofproduct' => 'table_schema',
            'masschangecarrierofproduct' => 'table_schema',
            'massupdatefeatureproducts' => 'table_schema',
            'masschangedescriptionofproduct' => 'description_change',
            'masschangespecificpriceofproduct' => 'table_schema',
            'masschangepriceofproduct' => 'price_change',
            'masschangecarrierbycategoryofproduct' => 'carrier_change',
            'masschangecombinationsofproduct' => 'table_schema',
            'masschangeimageofproduct' => 'table_schema',
            'masschangeaccessoriesofproduct' => 'table_schema',
            'massupdatepricebyid' => 'pricebyid',
            'masschangeproductavailable' => 'table_schema',
        );

        $this->initial_filters = array(
            'massupdateproducts' => true,
            'masschangecategoriesofproduct' => true,
            'masschangemetatagsofproduct' => true,
            'masschangecarrierofproduct' => true,
            'massupdatefeatureproducts' => true,
            'masschangedescriptionofproduct' => true,
            'masschangespecificpriceofproduct' => true,
            'masschangepriceofproduct' => true,
            'masschangecarrierbycategoryofproduct' => true,
            'masschangecombinationsofproduct' => true,
            'masschangeimageofproduct' => true,
            'masschangeaccessoriesofproduct' => true,
            'massupdatepricebyid' => false,
            'masschangeproductavailable' => true
        );

        $this->languages = Language::getLanguages(false);
        if ($this->languages) {
            foreach ($this->languages as &$language) {
                if (file_exists(_PS_ROOT_DIR_.'img/l/'.$language['id_lang'].'.jpg'))
                    $language['img_link'] = '/img/l/'.$language['id_lang'].'.jpg';
                elseif (file_exists(_PS_ROOT_DIR_.'/img/l/none.jpg'))
                    $language['img_link'] = _PS_LANG_IMG_DIR_.'none.jpg';
                else
                    $language['img_link'] = null;

                $language['is_default'] = !!($language['id_lang'] == $this->context->language->id);
                $language['is_current'] = !!($language['id_lang'] == $this->context->language->id);
            }
        }

        // Sort the tabs that need to be preloaded by their priority number
        asort($this->available_tabs, SORT_NUMERIC);

        $this->shop_group = new ShopGroup((int)Shop::getContextShopGroupID());
        $this->tax_rules_group = TaxRulesGroup::getTaxRulesGroups();

        if ($this->tax_rules_group) {
            $this->tax_rates = TaxRulesGroup::getAssociatedTaxRatesByIdCountry(Configuration::get('PS_COUNTRY_DEFAULT'));
            if ($this->tax_rates)
                foreach ($this->tax_rules_group as $i => $taxrule)
                    $this->tax_rules_group[$i]['rate'] = isset($this->tax_rates[$taxrule['id_tax_rules_group']]) ?
                        $this->tax_rates[$taxrule['id_tax_rules_group']] : 0;
        }

        $this->limit_conf = Configuration::get(Tools::strtoupper($this->module->name).'_LIMIT') ?
            Configuration::get(Tools::strtoupper($this->module->name).'_LIMIT') : 20;

        if (Configuration::get(Tools::strtoupper($this->module->name).'_OPTIONS'))
            $this->options = unserialize(Configuration::get(Tools::strtoupper($this->module->name).'_OPTIONS'));
    }

    public function initPageHeaderToolbar()
    {
        if (!$this->active_all) {
            $this->toolbar_title = $this->available_tabs_lang[$this->tab_display];
            /* $this->page_header_toolbar_btn['save'] = array(
              'href' => 'javascript:void(0);',
              'desc' => $this->l('Save')
              );

              if (isset($this->template_tabs[$this->tab_display]) && $this->template_tabs[$this->tab_display] == 'table_schema')
              {
              $this->page_header_toolbar_btn['delete'] = array(
              'href' => 'javascript:void(0);',
              'desc' => $this->l('Remove')
              );
              } */
        }
        parent::initPageHeaderToolbar();
    }

    public function initProcess()
    {
        $this->display = 'view';

        if (isset($this->available_tabs[Tools::getValue('tab')]))
            $this->tab_display = Tools::getValue('tab');

        // Set tab to display if not decided already
        if (!$this->tab_display && $this->action)
            if (in_array($this->action, array_keys($this->available_tabs)))
                $this->tab_display = $this->action;

        // And if still not set, use default
        if (!$this->tab_display) {
            if (in_array($this->default_tab, $this->available_tabs))
                $this->tab_display = $this->default_tab;
            else
                $this->tab_display = key($this->available_tabs);
        }

        if (in_array($this->tab_display, $this->initial_filters)) {
            $this->init_filters = $this->initial_filters[$this->tab_display];
        }
    }

    public function initHeader()
    {
        if (in_array($this->tab_display, array('masschangedescriptionofproduct', 'massupdatepricebyid'))) {
            $this->bootstrap = true;
        }
        parent::initHeader();
    }

    private function isActiveModule()
    {
        $shop = new Shop((int)$this->context->shop->id);
        $auth = new AuthDSMassUpdate('massupdateproducts');
        $licence = $auth->isActive($shop->getBaseURL());
        if (empty($licence)) {
            $domain = AuthDSMassUpdate::clearDomain($shop->getBaseURL());
            $licence = $auth->getStaticLicence($domain, 'Masowa aktualizacja produktów', Configuration::get('MASSUPDATEPRODUCTS_LICENCE'));
        }
        return array(
            'active' => $licence,
            'actived' => $licence['licence']->licence
        );
    }

    public function initContent()
    {
        $activeted = $this->isActiveModule();
        if ($activeted['actived'] == 1) {
            if ($this->active_all) {
                $this->context->smarty->assign(array(
                    'choose_shop' => true,
                    'phelpTop' => '',
                    'content' => $this->createTemplate('multishop_set.tpl')->fetch()
                ));
            } else {
                $ssl = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://';
                $this->addCSS(__DIR__ . '/../../assets/css/phelp.css');
                $module = Module::getInstanceByName('massupdateproducts');
                $mversion = $module->version;
                /* prestahelp API - do not remove! */
                $auth = new AuthDSMassUpdate('massupdateproducts');
                $productsShow = $auth->getBaners();
                $authorInfo = $auth->getAuthor();
                $chlogInfo = $auth->getChangelog();
                $chlogInfoOther = $auth->getChangelogOther();
                $currentVersion = $auth->getCurrentModuleVersion();
                $phelpBtm = __DIR__ . '/../../views/templates/admin/phelp-bottom.tpl';
                $phelpTop = __DIR__ . '/../../views/templates/admin/phelp-top.tpl';
                $moduleAssets = $ssl . $this->context->shop->domain . $this->context->shop->physical_uri . 'modules/massupdateproducts/assets/';
                $lastestVersion = $currentVersion['version'] == $mversion ? true : false;
                $updateLink = 'https://modules.prestahelp.com/moduly/massupdateproducts/massupdateproducts' . $currentVersion['version'] . '.zip';
                $indexLink = 'https://modules.prestahelp.com/moduly/massupdateproducts/';
                $banersHtml = AuthDSMassUpdate::getBanersHtml();
                $this->context->smarty->assign(array(
                    'moduleVersion' => $mversion,
                    'moduleAssets' => $moduleAssets,
                    'phelpBtm' => $phelpBtm,
                    'phelpTop' => $phelpTop,
                    'productsShow' => (array)$productsShow,
                    'authorInfo' => $authorInfo,
                    'chlogInfo' => $chlogInfo,
                    'moduleName' => $module->displayName,
                    'moduleNameInfo' => $module->name,
                    'currentModuleVersion' => $currentVersion['version'],
                    'lastestVersion' => $lastestVersion,
                    'updateLink' => $updateLink,
                    'chlogInfoOther' => $chlogInfoOther,
                    'indexLink' => $indexLink,
                    'updated_date' => date('d-m-Y', strtotime($activeted['active']['licence']->date_expire_update)),
                    'licence_update' => $activeted['active']['licence']->licence_update,
                    'support_date' =>date('d-m-Y', strtotime($activeted['active']['licence']->date_expire_support)),
                    'licence_date' => date('d-m-Y', strtotime($activeted['active']['licence']->date_expire)),
                    'activeted' => $activeted,
                    'licence' => $activeted['active']['licence'],
                    'banersHtml' => $banersHtml
                ));
                /* prestahelp API - do not remove! */

                $update_tabs = array();
                if (!array_key_exists($this->tab_display, $this->class_tabs) || !file_exists(_PS_MODULE_DIR_ .
                        $this->module->name . '/classes/' . $this->class_tabs[$this->tab_display] . '.php'))
                    $this->tab_display = $this->default_tab;

                require_once _PS_MODULE_DIR_ . $this->module->name . '/classes/' . $this->class_tabs[$this->tab_display] . '.php';

                if (!class_exists($this->class_tabs[$this->tab_display]))
                    $this->tab_display = $this->default_tab;

                $class_name = $this->class_tabs[$this->tab_display];

                $template = array_key_exists($this->tab_display, $this->template_tabs) ? $this->template_tabs[$this->tab_display] : 'table_schema';

                $this->addJs($this->module->getPath() . '/views/js/save_' . $template . '.js');
                $this->addJqueryUI('ui.draggable');
                $this->addJqueryUI('ui.droppable');
                $this->addJqueryUI('ui.sortable');
                $this->addJqueryUI('ui.slider');
                $this->addJqueryUI('ui.datepicker');

                // Load TinyMCE for tabs
                if (in_array($this->tab_display, array('masschangemetatagsofproduct'))) {
                    $ps16 = Tools::version_compare(_PS_VERSION_, '1.6.0.0', '>=');

                    $iso = $this->context->language->iso_code;

                    $iso_code = $ps16 ? (file_exists(_PS_CORE_DIR_ . '/js/tiny_mce/langs/' . $iso . '.js') ? $iso : 'en') : (file_exists(_PS_ROOT_DIR_ . '/js/tiny_mce/langs/' . $iso . '.js') ? $iso : 'en');

                    $this->context->smarty->assign(array(
                        'iso' => $iso_code,//$ps16 ? file_exists(_PS_CORE_DIR_ . '/js/tiny_mce/langs/' . $iso . '.js') ? $iso : 'en' : file_exists(_PS_ROOT_DIR_ . '/js/tiny_mce/langs/' . $iso . '.js') ? $iso : 'en',
                        'ad' => $ps16 ? __PS_BASE_URI__ . basename(_PS_ADMIN_DIR_) : dirname($_SERVER['PHP_SELF']),
                        'tinymce' => true,
                        'ps16' => $ps16
                    ));

                    if (_PS_VERSION_ < '1.7.0.0') {
                        $this->addJs(_PS_JS_DIR_ . 'tiny_mce/tinymce.min.js');
                        $this->context->controller->addJS(_PS_JS_DIR_ . 'tiny_mce/tiny_mce.js');
                        $this->context->controller->addJS(_PS_JS_DIR_ . 'tinymce.inc.js');
                    } else {
                        $this->context->controller->addJS(_PS_JS_DIR_ . 'tiny_mce/themes/modern/theme.min.js');
                        $this->context->controller->addJS(_PS_JS_DIR_ . 'tiny_mce/tiny_mce.js?1.7.6.1');
                        $this->context->controller->addJS(_PS_JS_DIR_ . 'admin/tinymce.inc.js?1.7.6.1');
                        $this->context->controller->addJS(_PS_JS_DIR_ . 'aadmin/tinymce_loader.js?1.7.6.1');
                    }
                }

                $i = 1;
                foreach (array_keys($this->available_tabs) as $update_tab) {
                    $update_tabs[$update_tab] = array(
                        'id_tab' => $update_tab,
                        'selected' => (Tools::strtolower($update_tab) == Tools::strtolower($this->tab_display) || (isset($this->tab_display_module) && 'module' .
                                $this->tab_display_module == Tools::strtolower($update_tab))),
                        'name' => $i++ . ') ' . $this->available_tabs_lang[$update_tab],
                        'href' => $this->context->link->getAdminLink('AdminMassUpdateProducts') . '&action=' . $update_tab
                    );
                }

                $this->context->smarty->assign(array(
                    'update_tabs' => $update_tabs,
                    'current' => $this->context->link->getAdminLink('AdminMassUpdateProducts'),
                    'token' => Tools::getAdminTokenLite('AdminMassUpdateProducts'),
                    'moduleURI' => _MODULE_DIR_ . $this->module->name . '/',
                    'type_img' => $this->img_type,
                    'img_lang_src' => _THEME_LANG_DIR_
                ));

                $object = new $class_name($this->module, $this, $this->context);

                if (Tools::isSubmit('is_ajax') && Tools::isSubmit('filter') && ($filter_name = Tools::getValue('filter_name'))) {
                    $products = $object->$filter_name(false, array(Tools::getValue('id_product')));
                    echo self::jsonEncode($object->displayAccessories($products, $filter_name));
                    die;
                }

                if (Tools::isSubmit('upload') && $this->tab_display == 'masschangeimageofproduct') {
                    echo self::jsonEncode($object->upload());
                    die;
                }

                if (Tools::getValue('is_ajax') && Tools::getValue('remove_mass')) {
                    $data_send = Tools::getValue('dataSend', array());
                    echo self::jsonEncode($object->removeMass(is_array($data_send) ? $data_send : array()));
                    die;
                }

                if (Tools::getValue('is_ajax') && Tools::getValue('save_mass')) {
                    $data_send = Tools::getValue('dataSend', array());
                    echo self::jsonEncode($object->save(is_array($data_send) ? $data_send : array()));
                    die;
                }

                if (Tools::getValue('is_ajax') && Tools::getValue('remove')) {
                    echo self::jsonEncode($object->remove());
                    die;
                }

                if (Tools::getValue('is_ajax') && Tools::getValue('save_fields')) {
                    echo self::jsonEncode($object->setFields(Tools::getValue('fields', array())));
                    die;
                }

                $this->addJs($this->module->getPath() . '/views/js/notify.min.js');
                $this->addJs($this->module->getPath() . '/views/js/process.js');
                $this->addJs($this->module->getPath() . '/views/js/multi.js');
                $this->addJs($this->module->getPath() . '/views/js/fileupload.js');
                $this->addJs($this->module->getPath() . '/views/js/fileupload2.js');
                $this->addJs($this->module->getPath() . '/views/js/tabs/' . $this->tab_display . '.js');
                $this->addCSS($this->module->getPath() . '/views/css/ph-panel.css');
                $this->addCSS($this->module->getPath() . '/views/css/massupdateproducts.css');
                $this->addCSS($this->module->getPath() . '/views/css/form.css');

                $default_currency = new Currency(Configuration::get('PS_CURRENCY_DEFAULT'), false, $this->context->shop->id);

                $this->context->smarty->assign(array(
                    'fields' => $object->getFields(),
                    'has_combination' => $object->hasCombination(),
                    'default_currency' => ValidateCore::isLoadedObject($default_currency) ? $default_currency : null
                ));

                /* new in 4.0.0 */
                $filters = array();
                if ($this->init_filters) {
                    $filters = $this->initFilters();
                }
                $show_mass_main_content = true;
                if ($this->available_tabs[$this->tab_display] == 15) {
                    $show_mass_main_content = false;
                }

                $this->context->smarty->assign(array(
                    'filters' => $filters,
                    'show_filters' => $this->init_filters,
                    'fields_content' => $this->template_tabs[$this->tab_display] == 'table_schema' ? $this->createTemplate('fields.tpl')->fetch() : '',
                    'show_mass_main_content' => $show_mass_main_content
                ));

                if (Tools::isSubmit('get_combinations') && Tools::getValue('is_ajax')) {
                    $id_product = Tools::getValue('id_product');
                    $product_object = new Product($id_product, false, $this->context->language->id, $this->context->shop->id);
                    $product_object->id_shop_object = $this->context->shop->id;
                    $message = '';

                    $combination_tmp = array();

                    if (($combinations = $product_object->getAttributeCombinations($this->context->language->id))) {
                        foreach ($combinations as $combination_arr) {
                            if (array_key_exists($combination_arr['id_product_attribute'], $combination_tmp))
                                continue;
                            $combination = new Combination($combination_arr['id_product_attribute'], null, $product_object->id_shop_object);
                            if (!ValidateCore::isLoadedObject($combination)) {
                                continue;
                            }
                            $message .= $object->displayCombination($product_object, $combination);
                            $combination_tmp[$combination->id] = true;

                            //$combination_object = new Combination($combination_array['id_product_attribute'], false, $this->context->shop->id);
                            //$message .= $object->displayCombination($product_object, $combination_object);
                        }
                    }
                    echo self::jsonEncode(array(
                        'result' => $message,
                        'table' => true,
                        'x' => 2
                    ));
                    die;
                }

                $ids = array();
                if (Tools::getValue('is_ajax') && Tools::getValue('refresh') && ($id_product = (int)Tools::getValue('id_product')))
                    $ids = array($id_product);

                $result = $object->filter(false, $ids);

                if ($result) {
                    if (($id_combination = (int)Tools::getValue('id_combination', 0))) {
                        $product = new Product($id_product, false, false, Tools::getValue('shop'));
                        if (!Validate::isLoadedObject($product))
                            $message = $this->module->l('Product not found');

                        $product->id_shop_object = Tools::getValue('shop');

                        $combination = new Combination($id_combination, false, Tools::getValue('shop'));
                        $message = '';
                        if (!Validate::isLoadedObject($combination))
                            $message = $this->module->l('Combination not found');
                        echo self::jsonEncode(array(
                            'result' => $message ? $message : $object->displayCombination($product, $combination),
                            'table' => true,
                            'x' => 1
                        ));
                        die;
                    }
                    echo self::jsonEncode(array_merge(
                        $object->display($result), $this->pager($result['datas']['product_count'])
                    ));
                    die;
                }

                $languages_ids = array();

                foreach ($this->languages as $language)
                    $languages_ids[] = $language['id_lang'];

                $this->context->smarty->assign(array(
                    'languages' => $this->languages,
                    'languages_ids' => $languages_ids,
                    'extra' => $object->extra(),
                    'widths' => MassUpdateProductsAbstract::getWidths(),
                    'show_filters' => $this->init_filters,
                    'phelpTop' => ''
                ));
                $this->context->smarty->assign(array(
                    'choose_shop' => false,
                    'content' => $this->createTemplate($template . '.tpl')->fetch()
                ));
            }
            parent::initContent();
        } else {
            if (Tools::getIsset('submitSaveLicenceCode')) {
                Configuration::updateValue('MASSUPDATEPRODUCTS_LICENCE', Tools::getValue('MASSUPDATEPRODUCTS_LICENCE'));
            }
            $showLicence = (isset($_GET['showLicence']) ? 1 : null);
            if ($showLicence == 1) {
                print_r($activeted);
            }
            $this->postProcess();
            $shop = new Shop((int)$this->context->shop->id);
            $this->context->smarty->assign(array(
                'moduleDomain' => $shop->getBaseURL(),
                'modConf' => 1,
                'licenceCode' => Configuration::get('MASSUPDATEPRODUCTS_LICENCE'),
                'licenceCodeGen' => Configuration::get('MASSUPDATEPRODUCTS_LICENCE_GEN'),
                'licGenData' => Configuration::get('MASSUPDATEPRODUCTS_LICENCE_GEN_DATA'),
                'showLicence' => $showLicence,
                'moduleURI' => _MODULE_DIR_ . $this->module->name . '/',
                'update_tabs' => array(),
                'show_mass_main_content' => false,
                'show_filters' => false,
                'banersHtml' => '',
                'phelpBtm' => '',
            ));
//            parent::initContent();

            $this->setTemplate('nolic.tpl');
        }
    }

    public function getCategoriesRecursive($id_category)
    {
        $result = array();
        $category = Db::getInstance()->getRow('SELECT a.id_category, a.level_depth, b.name, a.id_parent FROM `'._DB_PREFIX_.'category` as a
			JOIN `'._DB_PREFIX_.'category_shop` as c ON a.id_category = c.id_category AND c.id_shop = '.(int)$this->context->shop->id.'
			JOIN `'._DB_PREFIX_.'category_lang` as b ON a.id_category = b.id_category AND c.id_shop = b.id_shop AND b.id_lang = '
            .(int)$this->context->language->id
            .' WHERE a.id_category = '.(int)$id_category);

        if (!$category)
            return $result;

        $result[] = $category;

        $categories = Db::getInstance()->executeS('SELECT a.id_category, a.level_depth, b.name, a.id_parent FROM `'._DB_PREFIX_.'category` as a
			JOIN `'._DB_PREFIX_.'category_lang` as b ON a.id_category = b.id_category AND b.id_lang = '.(int)$this->context->language->id
            .' AND b.`id_shop` = '.pSQL($this->context->shop->id).' WHERE a.id_parent = '.(int)$id_category);
        if ($categories) {
            foreach ($categories as $category_child) {
                $result = array_merge($result, $this->getCategoriesRecursive($category_child['id_category']));
            }
        }

        return $result;
    }

    protected function initFilters()
    {
        $this->addJs(array(
            $this->module->getPath().'/views/js/filters.js',
            $this->module->getPath().'/views/js/fields.js',
            $this->module->getPath().'/views/js/sort.js',
        ));
        $this->addCSS(array(
            $this->module->getPath().'/views/css/filters.css',
            $this->module->getPath().'/views/css/fields.css',
            $this->module->getPath().'/views/css/sort.css',
            $this->module->getPath().'/views/css/table.css'
        ));

        $datas = $this->getDatas();

        $this->context->smarty->assign(array(
            'manufacturers' => Manufacturer::getManufacturers(),
            'categories' => $this->getCategoriesRecursive(Configuration::get('PS_HOME_CATEGORY')),
            'price_from' => floor($datas['p_min']),
            'price_to' => ceil($datas['p_max']),
            'quantity_from' => floor($datas['q_min']),
            'quantity_to' => ceil($datas['q_max']),
            'weight_from' => floor($datas['w_min']),
            'weight_to' => ceil($datas['w_max'])
        ));
        return $this->createTemplate('filters.tpl')->fetch();
    }

    public function getDatas($sql = null)
    {
        if (!$sql) {
            $from = 'FROM `'._DB_PREFIX_.'product` as p ';
            $join = 'JOIN `'._DB_PREFIX_.'product_lang` as pl ON p.id_product = pl.id_product AND pl.id_lang = '.(int)$this->context->language->id.' ';
            $join .= 'JOIN `'._DB_PREFIX_.'product_shop` as ps ON p.id_product = ps.id_product ';

            if (!$this->shop_group->share_stock)
                $join .= 'JOIN `'._DB_PREFIX_.'stock_available` as sa ON p.id_product = sa.id_product AND sa.id_product_attribute = 0 ';

            $sql = $from.$join;
        }

        return Db::getInstance()->getRow('SELECT MIN(ps.price) as p_min, MAX(ps.price) as p_max, '.
                ($this->shop_group->share_stock ? ' MIN(p.quantity) as q_min, MAX(p.quantity) as q_max, ' :
                    '  MIN(sa.quantity) as q_min, MAX(sa.quantity) as q_max,  ').'
					MIN(p.weight) as w_min, MAX(p.weight) as w_max '.$sql);
    }

    private function pager($product_count)
    {
        $page = (int)Tools::getValue('page', 0);
        if ($page < 1)
            $page = 1;
        $this->elements = (int)Tools::getValue('elements');
//        dump($this->elements);
        Configuration::updateValue('MASSUPDATEPRODUCT_PAGER', (int)$this->elements);
        $max = ceil($product_count / $this->elements);
        $pages = array();
        $larrows = !($page == 1);
        $rarrows = !($page >= $max);
        $i = $page - 2;
        for ($i; $i <= $page + 2; $i++)
            if ($i > 0 && $i <= $max)
                $pages[] = $i;
        $lmore = ($page - 2 > 1);
        $rmore = ($i <= $max);

        $this->context->smarty->assign(array(
            'max' => $max,
            'current' => $page,
            'pages' => $pages,
            'larrows' => $larrows,
            'rarrows' => $rarrows,
            'lmore' => $lmore,
            'rmore' => $rmore
        ));
	
        return array('pager' => $this->createTemplate('pager.tpl')->fetch());
    }

    public static function jsonEncode($data, $options = 0, $depth = 512)
    {
        return json_encode($data, $options, $depth);
    }
}
