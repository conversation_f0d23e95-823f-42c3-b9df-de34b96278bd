{"name": "prestashop/blockwishlist", "description": "PrestaShop module blockwishlist", "homepage": "https://github.com/PrestaShop/blockwishlist", "license": "AFL-3.0", "type": "prestashop-module", "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "config": {"platform": {"php": "7.2.5"}, "preferred-install": "dist", "classmap-authoritative": true, "optimize-autoloader": true, "sort-packages": true, "prepend-autoloader": false}, "require-dev": {"prestashop/php-dev-tools": "^4.3"}, "autoload": {"psr-4": {"PrestaShop\\Module\\BlockWishList\\": "src/"}, "classmap": ["blockwishlist.php", "controllers", "classes"]}, "scripts": {"set-license-header": ["@php ./vendor/bin/header-stamp --license=\"assets/afl.txt\" --exclude=\".github,.webpack,node_modules,vendor\""]}, "author": "PrestaShop"}